# Model and Tokenizer Configuration
model:
  # 在阿里云服务器上模型的本地路径, 或Hugging Face模型ID
  # 例如 'ZhipuAI/chatglm3-6b' 或下载到本地的 '/path/to/showui-2b'
  model_name_or_path: "path/on/aliyun/to/showui-2b"
  trust_remote_code: true

# Data Configuration
data:
  dataset_path: "data/raw/your_dataset.jsonl" # 你的数据集路径
  max_seq_length: 1024

# Training Configuration
training:
  output_dir: "outputs/checkpoints" # 模型保存路径
  num_train_epochs: 3
  per_device_train_batch_size: 1 # A10显存24G，可以尝试2或4
  gradient_accumulation_steps: 8 # 32的有效batch_size (1 * 8)
  learning_rate: 2e-4
  weight_decay: 0.01
  warmup_ratio: 0.1
  lr_scheduler_type: "cosine"
  logging_steps: 10
  save_steps: 100
  fp16: true # 如果A10支持bf16更好，但fp16是标配

# PEFT (LoRA) Configuration
peft:
  lora_r: 8
  lora_alpha: 32
  lora_dropout: 0.1
  lora_target_modules: # 需要根据ShowUI-2B模型结构确定，通常是'query_key_value'
    - "query_key_value"